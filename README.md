# Online Banking System Backend

A comprehensive REST API backend for an online banking system built with Spring Boot 3, Spring Security, and MySQL.

## Features

- **User Management**: Admin, Bank, and Customer user roles
- **Bank Management**: Register and manage multiple banks
- **Account Management**: Create and manage bank accounts
- **Transaction Processing**: Deposit, withdrawal, and transfer operations
- **Security**: JWT-based authentication and role-based authorization
- **Documentation**: Swagger/OpenAPI integration
- **Statement Generation**: PDF bank statement downloads

## Technology Stack

- **Framework**: Spring Boot 3.1.2
- **Security**: Spring Security with JWT
- **Database**: MySQL 8.0+
- **ORM**: Hibernate/JPA
- **Documentation**: SpringDoc OpenAPI 3
- **Build Tool**: Maven
- **Java Version**: 17

## Prerequisites

- Java 17 or higher
- MySQL 8.0 or higher
- Maven 3.6 or higher

## Setup Instructions

### 1. Database Setup

1. Install MySQL and create a database:
```sql
CREATE DATABASE online_banking_system;
```

2. Update database configuration in `src/main/resources/application.properties`:
```properties
spring.datasource.url=***********************************************************************************************
spring.datasource.username=your_username
spring.datasource.password=your_password
```

### 2. Application Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd online-banking-system-backend
```

2. Build the application:
```bash
mvn clean install
```

3. Run the application:
```bash
mvn spring-boot:run
```

The application will start on `http://localhost:8080`

### 3. API Documentation

Access Swagger UI at: `http://localhost:8080/swagger-ui.html`

## User Roles

### Admin
- Register banks and bank users
- View all banks and accounts
- Manage user accounts

### Bank
- Register customers
- Create bank accounts
- Process deposits and withdrawals
- View customer transactions
- Generate bank statements

### Customer
- View account details
- Transfer money between accounts
- View transaction history
- Download bank statements

## API Endpoints

### Authentication
- `POST /api/user/login` - User login
- `POST /api/user/admin/register` - Register admin (public)

### User Management
- `POST /api/user/register` - Register user (Bank/Admin only)
- `GET /api/bank/fetch/user` - Get users (Admin only)

### Bank Management
- `POST /api/bank/register` - Register bank (Admin only)
- `GET /api/bank/fetch/all` - Get all banks (Admin only)
- `GET /api/bank/fetch/id` - Get bank by ID

### Account Management
- `POST /api/bank/account/add` - Create bank account (Bank only)
- `GET /api/bank/account/fetch/all` - Get all accounts (Admin only)
- `GET /api/bank/account/fetch/user` - Get user accounts
- `GET /api/bank/account/fetch/bankwise` - Get bank accounts (Bank only)

### Transactions
- `POST /api/bank/transaction/deposit` - Deposit money (Bank only)
- `POST /api/bank/transaction/withdraw` - Withdraw money (Bank only)
- `POST /api/bank/transaction/account/transfer` - Transfer money (Customer only)
- `GET /api/bank/transaction/history` - Get transaction history
- `GET /api/bank/transaction/statement/download` - Download PDF statement

## Database Schema

### Users Table
- `id` (Primary Key)
- `first_name`
- `last_name`
- `email`
- `password` (encrypted)
- `phone_number`
- `address`
- `roles` (ADMIN, BANK, CUSTOMER)
- `status` (Active, Deactivated)

### Banks Table
- `id` (Primary Key)
- `name`
- `code`
- `ifsc_code`
- `address`

### Bank Accounts Table
- `id` (Primary Key)
- `number`
- `ifsc_code`
- `balance`
- `status` (Active, Deactivated)
- `user_id` (Foreign Key)
- `bank_id` (Foreign Key)

### Bank Account Transactions Table
- `id` (Primary Key)
- `transaction_id`
- `type` (Deposit, Withdraw, Transfer)
- `amount`
- `balance_after_transaction`
- `transaction_time`
- `bank_account_id` (Foreign Key)
- `bank_id` (Foreign Key)

## Security Configuration

The application uses JWT-based authentication with role-based access control:

- **JWT Token**: Required for all authenticated endpoints
- **CORS**: Configured for frontend integration
- **Password Encryption**: BCrypt hashing
- **Session Management**: Stateless

## Error Handling

Comprehensive global exception handling for:
- Authentication errors
- Authorization errors
- Validation errors
- Database errors
- Custom business logic errors

## Logging

- **Framework**: Log4j2
- **Levels**: Configurable logging levels
- **Output**: Console and file logging

## Development

### Running Tests
```bash
mvn test
```

### Building for Production
```bash
mvn clean package -Pprod
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License.
