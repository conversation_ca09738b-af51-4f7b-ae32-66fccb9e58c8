package com.onlinebankingsystem.exception;

import java.util.NoSuchElementException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import com.onlinebankingsystem.dto.CommonApiResponse;

@RestControllerAdvice
public class GlobalExceptionHandler {

	private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

	@ExceptionHandler(FailedToRegisterBankException.class)
	public ResponseEntity<CommonApiResponse> handleFailedToRegisterBankException(FailedToRegisterBankException ex) {
		logger.error("Failed to register bank: {}", ex.getMessage());
		String responseMessage = ex.getMessage();

		CommonApiResponse apiResponse = CommonApiResponse.builder()
				.responseMessage(responseMessage)
				.isSuccess(false)
				.build();
		return new ResponseEntity<CommonApiResponse>(apiResponse, HttpStatus.BAD_REQUEST);
	}

	@ExceptionHandler(BankAccountTransactionException.class)
	public ResponseEntity<CommonApiResponse> handleBankAccountTransactionException(BankAccountTransactionException ex) {
		logger.error("Bank account transaction error: {}", ex.getMessage());
		String responseMessage = ex.getMessage();

		CommonApiResponse apiResponse = CommonApiResponse.builder()
				.responseMessage(responseMessage)
				.isSuccess(false)
				.build();
		return new ResponseEntity<CommonApiResponse>(apiResponse, HttpStatus.BAD_REQUEST);
	}

	@ExceptionHandler(NoSuchElementException.class)
	public ResponseEntity<CommonApiResponse> handleNoSuchElementException(NoSuchElementException ex) {
		logger.error("Resource not found: {}", ex.getMessage());
		String responseMessage = "Requested resource not found";

		CommonApiResponse apiResponse = CommonApiResponse.builder()
				.responseMessage(responseMessage)
				.isSuccess(false)
				.build();
		return new ResponseEntity<CommonApiResponse>(apiResponse, HttpStatus.NOT_FOUND);
	}

	@ExceptionHandler(BadCredentialsException.class)
	public ResponseEntity<CommonApiResponse> handleBadCredentialsException(BadCredentialsException ex) {
		logger.error("Authentication failed: {}", ex.getMessage());
		String responseMessage = "Invalid credentials provided";

		CommonApiResponse apiResponse = CommonApiResponse.builder()
				.responseMessage(responseMessage)
				.isSuccess(false)
				.build();
		return new ResponseEntity<CommonApiResponse>(apiResponse, HttpStatus.UNAUTHORIZED);
	}

	@ExceptionHandler(AccessDeniedException.class)
	public ResponseEntity<CommonApiResponse> handleAccessDeniedException(AccessDeniedException ex) {
		logger.error("Access denied: {}", ex.getMessage());
		String responseMessage = "Access denied. You don't have permission to access this resource";

		CommonApiResponse apiResponse = CommonApiResponse.builder()
				.responseMessage(responseMessage)
				.isSuccess(false)
				.build();
		return new ResponseEntity<CommonApiResponse>(apiResponse, HttpStatus.FORBIDDEN);
	}

	@ExceptionHandler(MethodArgumentNotValidException.class)
	public ResponseEntity<CommonApiResponse> handleValidationException(MethodArgumentNotValidException ex) {
		logger.error("Validation error: {}", ex.getMessage());
		String responseMessage = "Validation failed: " + ex.getBindingResult().getFieldError().getDefaultMessage();

		CommonApiResponse apiResponse = CommonApiResponse.builder()
				.responseMessage(responseMessage)
				.isSuccess(false)
				.build();
		return new ResponseEntity<CommonApiResponse>(apiResponse, HttpStatus.BAD_REQUEST);
	}

	@ExceptionHandler(Exception.class)
	public ResponseEntity<CommonApiResponse> handleGenericException(Exception ex, WebRequest request) {
		logger.error("Unexpected error occurred: {}", ex.getMessage(), ex);
		String responseMessage = "An unexpected error occurred. Please try again later";

		CommonApiResponse apiResponse = CommonApiResponse.builder()
				.responseMessage(responseMessage)
				.isSuccess(false)
				.build();
		return new ResponseEntity<CommonApiResponse>(apiResponse, HttpStatus.INTERNAL_SERVER_ERROR);
	}

}
