# MySQL Properties
# Note: Update the port, username, and password according to your MySQL setup
spring.datasource.url=***********************************************************************************************
spring.datasource.username=root
# Enter your MySQL password below (currently set to empty for development)
spring.datasource.password=
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA/Hibernate Properties
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.open-in-view=false

# Server Configuration
server.port=8080
server.servlet.context-path=/

# Application Configuration
spring.application.name=online-banking-system

# Swagger/OpenAPI Configuration
springdoc.packagesToScan=com.onlinebankingsystem.controller
springdoc.pathsToMatch=/api/**
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.api-docs.path=/api-docs

# Logging Configuration (Log4j2 is configured via log4j2-spring.xml)
logging.level.com.onlinebankingsystem=INFO
logging.level.org.springframework.security=DEBUG